package org.jeecg.modules.digital_textbook.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description: digital_textbook
 * @Author: jeecg-boot
 * @Date:   2025-05-17
 * @Version: V1.0
 */
@Data
@TableName("digital_textbook")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="digital_textbook对象", description="digital_textbook")
public class DigitalTextbook implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "id")
    private String id;
	/**教材名称*/
    @NotEmpty(message = "教材名称不能为空")
	@Excel(name = "教材名称", width = 15)
    @ApiModelProperty(value = "教材名称")
    private String name;
	/**学科ID*/
    @NotEmpty(message = "学科ID不能为空")
	@Excel(name = "学科ID", width = 15)
    @ApiModelProperty(value = "学科ID")
    private String subjectId;
	/**学籍阶段ID*/
    @NotEmpty(message = "学籍阶段ID不能为空")
	@Excel(name = "学籍阶段ID", width = 15)
    @ApiModelProperty(value = "学籍阶段ID")
    private String stageId;
	/**出版年份*/
	@Excel(name = "出版年份", width = 15)
    @ApiModelProperty(value = "出版年份")
    private String publishYear;
	/**版本*/
	@Excel(name = "版本", width = 15)
    @ApiModelProperty(value = "版本")
    private String version;
	/**封面图*/
	@Excel(name = "封面图", width = 15)
    @ApiModelProperty(value = "封面图")
    private String coverImageRaw;
	/**createBy*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**updateBy*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
	/**updateTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
	/**sysOrgCode*/
    @ApiModelProperty(value = "sysOrgCode")
    private String sysOrgCode;

    @ApiModelProperty(value = "封底/编委会信息")
    private String info;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "学段名称")
    @TableField(exist = false)
    private String schoolStageName;

    @ApiModelProperty(value = "学科名称")
    @TableField(exist = false)
    private String subjectName;

    @NotEmpty(message = "教材文件code不能为空")
    @ApiModelProperty(value = "教材文件code")
    private String code;

    @ApiModelProperty(value = "教材文件")
    private String fileName;

    @TableField(exist = false)
    private Boolean isActive;

    @TableField(exist = false)
    private String title;

}
