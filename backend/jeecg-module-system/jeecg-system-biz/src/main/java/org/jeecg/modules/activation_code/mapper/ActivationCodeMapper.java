package org.jeecg.modules.activation_code.mapper;

import java.util.List;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.activation_code.entity.ActivationCode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: activation_code
 * @Author: jeecg-boot
 * @Date:   2025-05-17
 * @Version: V1.0
 */
public interface ActivationCodeMapper extends BaseMapper<ActivationCode> {

    /**
     * 根据标准化激活码查询并加锁
     * @param normalizedCode 标准化后的激活码
     * @return 激活码实体
     */
    @InterceptorIgnore(tenantLine = "true")
    @Select("SELECT TOP 1 * FROM activation_code WITH (UPDLOCK, ROWLOCK) " +
            "WHERE UPPER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(" +
            "code, '-', ''), '_', ''), ' ', ''), '.', ''), '/', ''), '\\\\', ''), '(', ''), ')', ''), '[', ''), ']', '')) = #{normalizedCode} " +
            "AND is_used = 0 " +
            "AND (expire_time IS NULL OR expire_time > GETDATE())")
    ActivationCode findByNormalizedCodeWithLock(@Param("normalizedCode") String normalizedCode);

}
