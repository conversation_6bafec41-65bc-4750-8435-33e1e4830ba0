import {BasicColumn} from '/src/components/Table';
import {FormSchema} from '/src/components/Table';
import { rules} from '/src/utils/helper/validator';
import { render } from '/src/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/src/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '教材名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '学科ID',
    align:"center",
    dataIndex: 'subjectId'
   },
   {
    title: '学籍阶段ID',
    align:"center",
    dataIndex: 'stageId'
   },
   {
    title: '出版年份',
    align:"center",
    dataIndex: 'publishYear'
   },
   {
    title: '版本',
    align:"center",
    dataIndex: 'version'
   },
   {
    title: '封面图',
    align:"center",
    dataIndex: 'coverImageRaw'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '教材名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入教材名称!'},
          ];
     },
  },
  {
    label: '学科ID',
    field: 'subjectId',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入学科ID!'},
          ];
     },
  },
  {
    label: '学籍阶段ID',
    field: 'stageId',
    component: 'InputNumber',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入学籍阶段ID!'},
          ];
     },
  },
  {
    label: '出版年份',
    field: 'publishYear',
    component: 'Input',
  },
  {
    label: '版本',
    field: 'version',
    component: 'Input',
  },
  {
    label: '封面图',
    field: 'coverImageRaw',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '教材名称',order: 0,view: 'text', type: 'string',},
  subjectId: {title: '学科ID',order: 1,view: 'number', type: 'number',},
  stageId: {title: '学籍阶段ID',order: 2,view: 'number', type: 'number',},
  publishYear: {title: '出版年份',order: 3,view: 'text', type: 'string',},
  version: {title: '版本',order: 4,view: 'text', type: 'string',},
  coverImageRaw: {title: '封面图',order: 5,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
